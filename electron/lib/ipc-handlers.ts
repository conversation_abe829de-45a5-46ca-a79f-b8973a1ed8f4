import { ipcMain, dialog } from 'electron'
import * as fs from 'fs'
import * as path from 'path'

import { figmaApiRequest, parseFigmaUrl, getFigmaNodeInfoFromUrl } from './figma-api'
import { downloadImages } from './image-downloader'

/**
 * 设置所有 IPC 处理器
 */
export function setupIpcHandlers(): void {
  // Figma API 请求处理器 - 只负责纯粹的API请求，不处理数据
  ipcMain.handle('figma-api-request', async (_event, { url, params, headers }) => {
    return await figmaApiRequest({ url, params, headers })
  })

  // Figma 链接解析处理器
  ipcMain.handle('parse-figma-url', async (_event, url: string) => {
    return await parseFigmaUrl(url)
  })

  // 图片下载处理器
  ipcMain.handle('download-images', async (_event, { imageUrls, downloadPath }) => {
    return await downloadImages({ imageUrls, downloadPath })
  })

  // 文件夹选择对话框处理器
  ipcMain.handle('show-folder-dialog', async (_event) => {
    try {
      const result = await dialog.showOpenDialog({
        properties: ['openDirectory'],
        title: '选择生成文件夹',
        buttonLabel: '选择文件夹'
      })

      if (result.canceled) {
        return {
          success: false,
          canceled: true
        }
      }

      return {
        success: true,
        data: {
          folderPath: result.filePaths[0]
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '选择文件夹失败'
      }
    }
  })

  // 验证并创建文件夹处理器
  ipcMain.handle('validate-and-create-folder', async (_event, folderPath: string) => {
    try {
      // 检查路径是否存在
      if (fs.existsSync(folderPath)) {
        // 检查是否是文件夹
        const stats = fs.statSync(folderPath)
        if (stats.isDirectory()) {
          return {
            success: true,
            data: {
              exists: true,
              isDirectory: true,
              message: '文件夹已存在'
            }
          }
        } else {
          return {
            success: false,
            error: '指定路径是一个文件，不是文件夹'
          }
        }
      } else {
        // 文件夹不存在，尝试创建
        try {
          fs.mkdirSync(folderPath, { recursive: true })
          return {
            success: true,
            data: {
              exists: false,
              created: true,
              message: '文件夹创建成功'
            }
          }
        } catch (createError) {
          return {
            success: false,
            error: `创建文件夹失败: ${createError instanceof Error ? createError.message : '未知错误'}`
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `验证文件夹失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  })

  // 初始化项目文件夹处理器
  ipcMain.handle('init-project', async (_event, { folderPath, domContent, designWidth }: { folderPath: string, domContent?: string, designWidth?: number }) => {
    return await initProjectFolder(folderPath, domContent, designWidth)
  })

  // HTML分析处理器
  ipcMain.handle('analyze-html-folder', async (_event, { folderPath, baseFigmaUrl }: { folderPath: string, baseFigmaUrl?: string }) => {
    return await analyzeHtmlFolder(folderPath, baseFigmaUrl)
  })



  // 生成完整提示词处理器
  ipcMain.handle('generate-complete-prompt', async (_event, { folderPath, nodeId, targetClassName }: { folderPath: string, nodeId: string, targetClassName: string }) => {
    return await generateCompletePrompt(folderPath, nodeId, targetClassName)
  })

  // 读取文件内容处理器
  ipcMain.handle('read-file-content', async (_event, filePath: string) => {
    return await readFileContent(filePath)
  })

  // 写入文件内容处理器
  ipcMain.handle('write-file-content', async (_event, { filePath, content }: { filePath: string, content: string }) => {
    return await writeFileContent(filePath, content)
  })
}

/**
 * 读取文件内容
 * @param filePath 文件路径
 * @returns Promise<{success: boolean, data?: string, error?: string}>
 */
async function readFileContent(filePath: string): Promise<{success: boolean, data?: string, error?: string}> {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`)
    }

    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf-8')
    console.log(`📄 已读取文件: ${filePath}`)

    return {
      success: true,
      data: content
    }
  } catch (error) {
    console.error('❌ 读取文件失败:', error)
    return {
      success: false,
      error: `读取文件失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }
}

/**
 * 写入文件内容
 * @param filePath 文件路径
 * @param content 要写入的内容
 * @returns Promise<{success: boolean, error?: string}>
 */
async function writeFileContent(filePath: string, content: string): Promise<{success: boolean, error?: string}> {
  try {
    // 写入文件内容
    fs.writeFileSync(filePath, content, 'utf-8')
    console.log(`📝 已写入文件: ${filePath}`)

    return {
      success: true
    }
  } catch (error) {
    console.error('❌ 写入文件失败:', error)
    return {
      success: false,
      error: `写入文件失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }
}

/**
 * 初始化项目文件夹
 * 1. 创建文件夹
 * 2. 复制template.html到新文件夹并重命名为index.html
 * 3. 如果提供了DOM内容，将其插入到index.html中
 * 4. 如果提供了设计稿宽度，替换模板中的宽度值
 * 5. 创建assets子目录
 */
async function initProjectFolder(folderPath: string, domContent?: string, designWidth?: number): Promise<{success: boolean, data?: any, error?: string}> {
  try {
    // 1. 创建主文件夹（如果不存在）
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true })
      console.log(`📁 创建文件夹: ${folderPath}`)
    }

    // 2. 复制template.html到新文件夹并重命名为index.html
    const templatePath = path.join(process.cwd(), 'template.html')
    const targetIndexPath = path.join(folderPath, 'index.html')

    if (!fs.existsSync(templatePath)) {
      throw new Error('template.html文件不存在于项目根目录')
    }

    // 读取模板内容
    let templateContent = fs.readFileSync(templatePath, 'utf-8')

    // 如果提供了设计稿宽度，替换模板中的宽度值
    if (designWidth && designWidth > 0) {
      templateContent = templateContent.replace(/const designWidth = 393;/, `const designWidth = ${designWidth};`)
      console.log(`📐 已将设计稿宽度设置为: ${designWidth}px`)
    }

    // 如果提供了DOM内容，将其插入到body标签中
    if (domContent) {
      templateContent = templateContent.replace('<body>', `<body>\n  ${domContent}`)
      console.log('📄 已将DOM结构插入到index.html中')
    }

    // 写入index.html文件
    fs.writeFileSync(targetIndexPath, templateContent, 'utf-8')
    console.log(`📄 创建index.html: ${targetIndexPath}`)

    // 3. 创建assets子目录
    const assetsPath = path.join(folderPath, 'assets')
    if (!fs.existsSync(assetsPath)) {
      fs.mkdirSync(assetsPath, { recursive: true })
      console.log(`📁 创建assets目录: ${assetsPath}`)
    }

    return {
      success: true,
      data: {
        folderPath,
        indexPath: targetIndexPath,
        assetsPath,
        message: '项目文件夹初始化成功'
      }
    }

  } catch (error) {
    console.error('❌ 初始化项目文件夹失败:', error)
    return {
      success: false,
      error: `初始化项目文件夹失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }
}

/**
 * 分析HTML文件夹中的index.html，找到未定义样式的类名并获取Figma节点信息
 * @param folderPath 文件夹路径
 * @param baseFigmaUrl 可选的基础Figma URL
 * @returns Promise<{success: boolean, data?: any, error?: string}>
 */
async function analyzeHtmlFolder(folderPath: string, baseFigmaUrl?: string): Promise<{success: boolean, data?: any, error?: string}> {
  try {
    // 1. 检查文件夹路径是否存在
    if (!fs.existsSync(folderPath)) {
      throw new Error(`文件夹路径不存在: ${folderPath}`);
    }

    // 2. 查找index.html文件
    const indexHtmlPath = path.join(folderPath, 'index.html');
    if (!fs.existsSync(indexHtmlPath)) {
      throw new Error(`index.html文件不存在: ${indexHtmlPath}`);
    }

    // 3. 读取HTML文件内容
    const htmlContent = fs.readFileSync(indexHtmlPath, 'utf-8');

    // 4. 解析HTML内容，提取所有class属性
    const classNames = extractClassNames(htmlContent);
    console.log('🔍 提取到的所有class名称:', classNames);

    // 5. 提取style标签中定义的CSS类名
    const definedClasses = extractDefinedClasses(htmlContent);
    console.log('🎨 style标签中定义的类名:', Array.from(definedClasses));

    // 6. 找到存在于DOM中但不存在于style标签中的类名
    const undefinedClasses = classNames.filter(className => !definedClasses.has(className));
    console.log('❌ 未定义样式的类名:', undefinedClasses);

    if (undefinedClasses.length === 0) {
      console.log('✅ 所有类名都已定义样式');
      return {
        success: true,
        data: {
          message: '所有类名都已定义样式',
          allClasses: classNames,
          definedClasses: Array.from(definedClasses),
          undefinedClasses: []
        }
      };
    }

    // 7. 取第一个未定义的类名进行处理
    const targetClassName = undefinedClasses[0];
    console.log('🎯 选择处理的类名:', targetClassName);

    // 8. 检查类名格式是否符合 "_12:14" 这样的格式
    if (!targetClassName.startsWith('_')) {
      throw new Error(`类名格式不符合要求，应该以下划线开头: ${targetClassName}`);
    }

    // 9. 去除开头的下划线，得到ID
    const nodeId = targetClassName.substring(1);
    console.log('🔑 提取到的节点ID:', nodeId);

    // 10. 构造Figma URL
    let figmaUrl: string;
    if (baseFigmaUrl) {
      figmaUrl = `${baseFigmaUrl}?node-id=${nodeId.replace('-', ':')}&t=0cP6qb0lWwh00jvz-4`;
    } else {
      // 使用默认的Figma URL
      const defaultBaseFigmaUrl = 'https://www.figma.com/design/w7ZjgUryNqGb1UPKXCOoO6/Task-Management--Todo-App--Community-';
      figmaUrl = `${defaultBaseFigmaUrl}?node-id=${nodeId.replace('-', ':')}&t=0cP6qb0lWwh00jvz-4`;
    }

    console.log('🔗 构造的Figma URL:', figmaUrl);

    // 11. 使用getFigmaNodeInfoFromUrl获取节点信息
    console.log('📡 正在获取Figma节点信息...');
    const nodeInfoResult = await getFigmaNodeInfoFromUrl(figmaUrl, nodeId);

    if (!nodeInfoResult.success) {
      throw new Error(`获取Figma节点信息失败: ${nodeInfoResult.error}`);
    }

    console.log('✅ 成功获取Figma节点信息');

    return {
      success: true,
      data: {
        targetClassName,
        nodeId,
        figmaUrl,
        figmaInfo: nodeInfoResult.data.figmaInfo,
        allClasses: classNames,
        definedClasses: Array.from(definedClasses),
        undefinedClasses
      }
    };

  } catch (error) {
    console.error('❌ 分析HTML文件夹失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 从HTML内容中提取所有class属性值
 * @param htmlContent HTML内容
 * @returns string[] 所有class名称的数组
 */
function extractClassNames(htmlContent: string): string[] {
  const classNames = new Set<string>();

  // 使用正则表达式匹配所有class属性
  const classRegex = /class\s*=\s*["']([^"']+)["']/gi;
  let match;

  while ((match = classRegex.exec(htmlContent)) !== null) {
    const classValue = match[1];
    // 分割多个类名（用空格分隔）
    const individualClasses = classValue.split(/\s+/).filter(cls => cls.trim().length > 0);
    individualClasses.forEach(cls => classNames.add(cls.trim()));
  }

  return Array.from(classNames);
}

/**
 * 从HTML内容的style标签中提取已定义的CSS类名
 * @param htmlContent HTML内容
 * @returns Set<string> 已定义的类名集合
 */
function extractDefinedClasses(htmlContent: string): Set<string> {
  const definedClasses = new Set<string>();

  // 提取所有style标签内容
  const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
  let match;

  while ((match = styleRegex.exec(htmlContent)) !== null) {
    const styleContent = match[1];

    // 从CSS内容中提取类选择器
    const classSelectors = extractCssClassSelectors(styleContent);
    classSelectors.forEach(cls => definedClasses.add(cls));
  }

  return definedClasses;
}

/**
 * 从CSS内容中提取类选择器
 * @param cssContent CSS内容
 * @returns string[] 类选择器数组
 */
function extractCssClassSelectors(cssContent: string): string[] {
  const classNames = new Set<string>();

  // 匹配CSS类选择器 .className (支持转义字符)
  // 更新正则表达式以匹配包含转义字符的类名
  const classRegex = /\.([a-zA-Z_][a-zA-Z0-9_\\:-]*)/g;
  let match;

  while ((match = classRegex.exec(cssContent)) !== null) {
    let className = match[1];

    // 处理转义字符：将 \: 转换为 :
    className = className.replace(/\\:/g, ':');

    classNames.add(className);
  }

  return Array.from(classNames);
}



/**
 * 生成完整的提示词
 * @param folderPath 项目文件夹路径
 * @param nodeId 节点ID
 * @param targetClassName 目标类名
 * @returns Promise<{success: boolean, data?: any, error?: string}>
 */
async function generateCompletePrompt(folderPath: string, nodeId: string, targetClassName: string): Promise<{success: boolean, data?: any, error?: string}> {
  try {
    console.log('🚀 开始生成完整提示词...');
    console.log(`📁 项目路径: ${folderPath}`);
    console.log(`🔑 节点ID: ${nodeId}`);
    console.log(`🎯 目标类名: ${targetClassName}`);

    // 1. 读取提示词模板
    const templatePath = path.join(process.cwd(), 'src/prompts/生成基础dom.text');
    if (!fs.existsSync(templatePath)) {
      throw new Error(`提示词模板文件不存在: ${templatePath}`);
    }

    let promptTemplate = fs.readFileSync(templatePath, 'utf-8');
    console.log('📄 已读取提示词模板');

    // 2. 读取HTML文件内容
    const indexHtmlPath = path.join(folderPath, 'index.html');
    if (!fs.existsSync(indexHtmlPath)) {
      throw new Error(`index.html文件不存在: ${indexHtmlPath}`);
    }

    const htmlContent = fs.readFileSync(indexHtmlPath, 'utf-8');
    console.log('📄 已读取HTML文件内容');

    // 3. 读取assets目录文件列表
    const assetsPath = path.join(folderPath, 'assets');
    let imageInfo = '';

    if (fs.existsSync(assetsPath)) {
      try {
        const files = fs.readdirSync(assetsPath);
        // 过滤出图片文件
        const imageFiles = files.filter(file => {
          const ext = path.extname(file).toLowerCase();
          return ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext);
        });

        if (imageFiles.length > 0) {
          imageInfo = imageFiles.join(',');
          console.log(`🖼️ 找到 ${imageFiles.length} 个图片文件: ${imageInfo}`);
        } else {
          imageInfo = '无图片文件';
          console.log('📁 assets目录中没有找到图片文件');
        }
      } catch (error) {
        console.warn('⚠️ 读取assets目录失败:', error);
        imageInfo = '读取失败';
      }
    } else {
      imageInfo = 'assets目录不存在';
      console.log('📁 assets目录不存在');
    }

    // 4. 替换模板中的变量
    let completePrompt = promptTemplate;

    // 替换 {{htmlInfo}}
    completePrompt = completePrompt.replace(/\{\{htmlInfo\}\}/g, htmlContent);

    // 替换 {{nodeId}}
    completePrompt = completePrompt.replace(/\{\{nodeId\}\}/g, nodeId);

    // 替换 {{targetClassName}}
    completePrompt = completePrompt.replace(/\{\{targetClassName\}\}/g, targetClassName);

    // 替换 {{imageInfo}}
    completePrompt = completePrompt.replace(/\{\{imageInfo\}\}/g, imageInfo);

    // 5. 打印完整的提示词
    console.log('\n' + '='.repeat(80));
    console.log('📝 完整提示词内容:');
    console.log('='.repeat(80));
    console.log(completePrompt);
    console.log('='.repeat(80) + '\n');

    return {
      success: true,
      data: {
        prompt: completePrompt,
        htmlContent,
        nodeId,
        targetClassName,
        imageInfo,
        message: '提示词生成完成'
      }
    };

  } catch (error) {
    console.error('❌ 生成完整提示词失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}



