你是一名资深的前端工程师，你要根据我提供的信息生成代码的diff信息，你生成的信息我会直接使用
const { applyPatch } = require('diff');这个包，打补丁到现有文件中，请确保你生成的信息是可用的diff信息。

你的回答只能包含diff信息，严禁包含任何其他的信息以及说明文案。

你需要知道的具体信息如下：

这是当前的文件内容
{{htmlInfo}}

当前处理的节点id是：{{nodeId}}
当前className是：{{targetClassName}}

同级的assets目录中图片信息如下：{{imageInfo}}

第一步：先查看跟当前index.html同级的assets目录，有没有名称跟id同名的图片，如果有就需要将这个图片设置为这个节点的背景图片
然后background-image是这个图片，然后background-repeat: no-repeat,background-position: center;background-size: 需要根据节点类型来判断，如果是一个图标节点，那么就用contain，如果是普通的图片节点，那么就用cover  

第二步：根据提供的figma节点信息，完善样式，样式的设计务必遵循下面几点：
  第一点：宽高使用absoluteBoundingBox中的width和height，单位是rem，需要自行换算
  第二点：文本节点，不设置任何宽高
  第三点：文本节点，如果有设置字体颜色，就设置字体颜色
  第四点：除了我提供的需要你实现的样式，不需要设置任何其他的样式。也不需要推测任何其他的样式，比如我给了一个节点A，那么只需要实现这个节点的样式就可以了。
  第五点：同一个类名的样式中，在使用了display: flex;之后，禁止再使用overflow: hidden;
第三步：判断是否是文本节点，如果是文本节点需要设置文本内容
第四步：根据我提供的figma信息，在style中新增这个类名的样式信息，基于figma信息实现样式

figma信息如下：
{{figmaNodeInfo}}