<template>
  <div class="page">
    <!-- 输入界面 -->
    <div v-if="!showComparison" class="input-container">
      <div class="input-section">
        <h1 class="title">UI 设计对比工具</h1>
        <p class="description">输入页面链接开始对比设计稿与AI实现效果</p>

        <div class="input-group">
          <input
            v-model="urlInput"
            type="url"
            placeholder="请输入Figma分享链接"
            class="url-input"
            @keyup.enter="startComparison"
          >
        </div>

        <div class="folder-selection">
          <label class="folder-label">目标文件夹</label>
          <div class="folder-input-group">
            <input
              v-model="selectedFolder"
              type="text"
              placeholder="选择或输入目标文件夹路径"
              class="folder-input"
              @keyup.enter="startComparison"
            >
            <button
              @click="selectFolder"
              class="folder-button"
              type="button"
              :disabled="isValidating"
            >
              {{ isValidating ? '初始化中...' : '选择文件夹' }}
            </button>
          </div>
          <div class="folder-tips">
            <p v-if="!isValidating">💡 可以直接输入路径或点击按钮选择文件夹</p>
            <p v-if="!isValidating">📁 如果文件夹不存在，系统会自动创建并初始化项目结构</p>
            <p v-if="isValidating" class="validating-tip">🔍 正在验证文件夹并生成页面结构...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 对比界面 -->
    <div v-else class="container">
      <div class="back-button" @click="goBack">
        <span>← 返回</span>
      </div>

      <div class="comparison-container">
        <div class="left-section">
          <div class="phone-mockup">
            <div class="phone-screen" @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop">
              <div id="design-placeholder" class="phone-placeholder" v-show="!designImageSrc">
                <div style="font-size: 32px; margin-bottom: 10px;">📱</div>
                <p>拖拽或上传设计稿</p>
              </div>
              <img
                id="design-image"
                class="design-image"
                v-show="designImageSrc"
                :src="designImageSrc"
                alt="设计稿"
              >
            </div>
            <div class="phone-buttons">
              <div class="phone-button volume-up"></div>
              <div class="phone-button volume-down"></div>
              <div class="phone-button power"></div>
            </div>
          </div>
        </div>

        <div class="right-section">
          <div class="phone-mockup">
            <div class="phone-screen">
              <div id="iframe-placeholder" class="phone-placeholder-iframe" v-show="!showIframe">
                <div style="font-size: 32px; margin-bottom: 10px;">🌐</div>
                <p>加载页面中...</p>
              </div>
              <iframe
                ref="implementationIframe"
                id="implementation-iframe"
                class="implementation-iframe"
                v-show="showIframe"
                :src="iframeSrc"
              ></iframe>
            </div>
            <div class="phone-buttons">
              <div class="phone-button volume-up"></div>
              <div class="phone-button volume-down"></div>
              <div class="phone-button power"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { getFigmaInfoFromUrl, formatFigmaToBasicDom, extractDesignWidth, initializeFigmaWithImages } from '../api/figma'
import { analyzeHtmlAndGetFigmaInfoWithUrl } from '../lib/html-analyzer'
import { callClaude } from '../lib/claude'
// 移除diff依赖，改用直接CSS插入
// 界面状态
const showComparison = ref(true)
const urlInput = ref('')
const selectedFolder = ref('')
const isValidating = ref(false)

// 递归处理状态
const isProcessingRecursively = ref(false)
const recursiveProcessCount = ref(0)
const maxRecursiveAttempts = 10 // 最大递归次数，防止无限循环

/**
 * 将CSS样式插入到HTML文件的style标签中
 * @param htmlContent HTML文件内容
 * @param cssStyle CSS样式字符串，格式如 ".className { width: 1rem; }"
 * @returns 更新后的HTML内容
 */
const insertCssIntoHtml = (htmlContent: string, cssStyle: string): string => {
  try {
    // 清理CSS样式，移除多余的空白和换行
    const cleanedCss = cssStyle.trim()

    if (!cleanedCss) {
      console.warn('⚠️ CSS样式为空，跳过插入')
      return htmlContent
    }

    console.log('🎨 准备插入的CSS样式:', cleanedCss)

    // 查找</style>标签的位置
    const styleEndTagIndex = htmlContent.indexOf('</style>')

    if (styleEndTagIndex === -1) {
      console.warn('⚠️ 未找到</style>标签，尝试查找<style>标签并创建</style>')

      // 如果没有</style>，查找<style>标签
      const styleStartTagIndex = htmlContent.indexOf('<style>')
      if (styleStartTagIndex !== -1) {
        // 找到<style>标签，在其后添加CSS和</style>
        const styleTagEnd = htmlContent.indexOf('>', styleStartTagIndex) + 1
        const beforeStyle = htmlContent.substring(0, styleTagEnd)
        const afterStyle = htmlContent.substring(styleTagEnd)

        const cssToInsert = `\n    ${cleanedCss}\n  </style>`
        const updatedHtml = beforeStyle + cssToInsert + afterStyle.replace('</style>', '')

        console.log('✅ CSS样式插入成功（创建了</style>标签）')
        return updatedHtml
      } else {
        console.warn('⚠️ 未找到<style>标签，无法插入CSS')
        return htmlContent
      }
    }

    // 在</style>标签前插入新的CSS样式
    const beforeStyle = htmlContent.substring(0, styleEndTagIndex)
    const afterStyle = htmlContent.substring(styleEndTagIndex)

    // 确保CSS样式前后有适当的换行和缩进
    const cssToInsert = `\n    ${cleanedCss}\n  `

    const updatedHtml = beforeStyle + cssToInsert + afterStyle

    console.log('✅ CSS样式插入成功')
    return updatedHtml

  } catch (error) {
    console.error('❌ 插入CSS样式失败:', error)
    return htmlContent
  }
}

// 对比页面状态
const designImageSrc = ref('https://fangimg.oss-cn-beijing.aliyuncs.com/test.png')
const showIframe = ref(true)
const iframeSrc = ref('/sss/ppp/index.html')
const implementationIframe = ref<HTMLIFrameElement | null>(null)

/**
 * 递归处理未定义样式的类名
 * 持续检查HTML中是否还有未定义CSS的类名，如果有就继续调用Claude API生成CSS并写入
 * @param targetFolder 目标文件夹路径
 * @param inputUrl Figma链接
 * @param designWidth 设计稿宽度
 * @param summary 图片下载摘要
 */
const processUndefinedClassesRecursively = async (targetFolder: string, inputUrl: string, designWidth: number, summary: any) => {
  isProcessingRecursively.value = true
  recursiveProcessCount.value = 0

  console.log('🔄 开始递归处理未定义样式的类名...')

  try {
    while (recursiveProcessCount.value < maxRecursiveAttempts) {
      recursiveProcessCount.value++
      console.log(`🔄 第 ${recursiveProcessCount.value} 轮递归处理...`)

      // 分析HTML文件，检查是否还有未定义样式的类名
      const analysisResult = await analyzeHtmlAndGetFigmaInfoWithUrl(targetFolder, inputUrl)
      console.log(`🔍 第 ${recursiveProcessCount.value} 轮分析结果:`, analysisResult)

      // 如果没有未定义的类名，说明所有样式都已完成
      if (!analysisResult || !analysisResult.targetClassName || !analysisResult.nodeId || !analysisResult.figmaInfo) {
        console.log('✅ 所有类名都已定义样式，递归处理完成！')
        alert(`🎉 CSS样式生成完成！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\n🖼️ 已下载 ${summary.downloadedCount} 个图片到assets文件夹\n🔄 共进行了 ${recursiveProcessCount.value} 轮CSS生成\n💡 所有DOM元素都已有完整的CSS样式`)
        break
      }

      // 如果还有未定义的类名，继续处理
      console.log(`❌ 发现未定义样式的类名: ${analysisResult.targetClassName}，继续生成CSS...`)

      // 生成完整提示词
      const promptResult = await window.ipcRenderer.invoke('generate-complete-prompt', {
        folderPath: targetFolder,
        nodeId: analysisResult.nodeId,
        targetClassName: analysisResult.targetClassName,
        figmaUrl: inputUrl
      })

      if (!promptResult.success) {
        console.error('❌ 生成提示词失败:', promptResult.error)
        continue
      }

      // 调用Claude API生成CSS代码
      const claudeResponse = await callClaude(promptResult.data.prompt, {
        model: 'claude-3-5-sonnet-20241022',
        maxTokens: 4000,
        temperature: 0.3
      })

      console.log(`🤖 第 ${recursiveProcessCount.value} 轮Claude API调用成功`)

      // 打印Claude的完整响应内容
      console.log('\n' + '='.repeat(80))
      console.log(`🎨 第 ${recursiveProcessCount.value} 轮Claude完整响应内容:`)
      console.log('='.repeat(80))
      console.log(claudeResponse)
      console.log('='.repeat(80) + '\n')

      // 读取当前HTML文件内容
      const indexHtmlPath = `${targetFolder}/index.html`
      const fileResult = await window.ipcRenderer.invoke('read-file-content', indexHtmlPath)

      if (!fileResult.success) {
        console.error('❌ 读取HTML文件失败:', fileResult.error)
        continue
      }

      // 插入CSS样式到HTML文件
      const originalCode = fileResult.data
      console.log(`📄 第 ${recursiveProcessCount.value} 轮原始文件长度:`, originalCode.length)
      console.log(`🎨 第 ${recursiveProcessCount.value} 轮Claude响应长度:`, claudeResponse.length)

      const updatedCode = insertCssIntoHtml(originalCode, claudeResponse)

      if (updatedCode === originalCode) {
        console.error('❌ CSS样式插入失败，文件内容未发生变化')
        console.log('🔍 原始代码片段:', originalCode.substring(0, 500))
        console.log('🔍 Claude响应片段:', claudeResponse.substring(0, 500))
        continue
      }

      console.log(`📝 第 ${recursiveProcessCount.value} 轮更新后文件长度:`, updatedCode.length)
      console.log(`🔍 文件是否有变化:`, originalCode !== updatedCode)

      const writeResult = await window.ipcRenderer.invoke('write-file-content', {
        filePath: indexHtmlPath,
        content: updatedCode
      })

      if (!writeResult.success) {
        console.error('❌ 写入文件失败:', writeResult.error)
        continue
      }

      console.log(`✅ 第 ${recursiveProcessCount.value} 轮CSS更新完成`)

      // 验证文件是否真的被更新了
      const verifyResult = await window.ipcRenderer.invoke('read-file-content', indexHtmlPath)
      if (verifyResult.success) {
        console.log(`🔍 第 ${recursiveProcessCount.value} 轮验证文件长度:`, verifyResult.data.length)
        console.log(`✅ 文件写入验证:`, verifyResult.data === updatedCode ? '成功' : '失败')
      }

      // 短暂延迟，避免过于频繁的API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    // 如果达到最大递归次数
    if (recursiveProcessCount.value >= maxRecursiveAttempts) {
      console.warn(`⚠️ 已达到最大递归次数 ${maxRecursiveAttempts}，停止处理`)
      alert(`⚠️ CSS样式生成已达到最大尝试次数\n🔄 共进行了 ${recursiveProcessCount.value} 轮CSS生成\n💡 请检查生成的页面效果，可能还有部分样式需要手动调整`)
    }

  } catch (error) {
    console.error('❌ 递归处理过程中发生错误:', error)
    alert(`❌ CSS样式生成过程中发生错误\n🔄 已完成 ${recursiveProcessCount.value} 轮CSS生成\n错误信息: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    isProcessingRecursively.value = false
  }
}

// 开始对比
const startComparison = async () => {
  if (!urlInput.value.trim()) {
    alert('请输入Figma分享链接')
    return
  }

  if (!selectedFolder.value.trim()) {
    alert('请选择目标文件夹')
    return
  }

  const inputUrl = urlInput.value.trim()
  const targetFolder = selectedFolder.value.trim()

  // 验证并初始化目标文件夹
  isValidating.value = true
  let initSuccess = false
  try {
    console.log('🔍 验证目标文件夹:', targetFolder)
    const folderValidation = await window.ipcRenderer.invoke('validate-and-create-folder', targetFolder)

    if (!folderValidation.success) {
      alert(`文件夹验证失败: ${folderValidation.error}`)
      return
    }

    // 如果文件夹不存在（刚创建），则进行初始化
    if (folderValidation.data.created) {
      console.log('📁 文件夹不存在，开始初始化项目...')

      // 先获取Figma数据并生成DOM结构
      let domContent = ''
      let designWidth = 393 // 默认宽度
      let initializationResult = null

      try {
        if (inputUrl.includes('figma.com')) {
          console.log('🔍 获取Figma数据并生成DOM结构...')
          const figmaInfo = await getFigmaInfoFromUrl(inputUrl)
          domContent = formatFigmaToBasicDom(figmaInfo)
          designWidth = extractDesignWidth(figmaInfo)
          console.log('✅ DOM结构和设计稿宽度获取成功')
        }
      } catch (figmaError) {
        console.warn('⚠️ 获取Figma数据失败，将创建空的index.html:', figmaError)
      }

      // 初始化项目文件夹结构
      const initResult = await window.ipcRenderer.invoke('init-project', {
        folderPath: targetFolder,
        domContent,
        designWidth
      })

      if (!initResult.success) {
        alert(`项目初始化失败: ${initResult.error}`)
        return
      }

      console.log('✅ 项目初始化成功:', initResult.data)

      // 如果是Figma链接，自动下载图片到assets文件夹
      if (inputUrl.includes('figma.com') && initResult.data.assetsPath) {
        try {
          console.log('🖼️ 开始自动下载Figma图片...')
          initializationResult = await initializeFigmaWithImages(inputUrl, initResult.data.assetsPath)

          if (initializationResult.downloadedImages) {
            const { summary } = initializationResult.downloadedImages
            console.log(`✅ 图片下载完成! 成功下载 ${summary.downloadedCount}/${summary.totalImages} 个图片`)

            // 资源下载完成后，开始递归处理未定义样式的类名
            try {
              console.log('🔍 开始递归处理所有未定义样式的类名...')
              await processUndefinedClassesRecursively(targetFolder, inputUrl, designWidth, summary)
              if (analysisResult && analysisResult.targetClassName && analysisResult.nodeId && analysisResult.figmaInfo) {
                try {
                  // 首先生成完整提示词并打印
                  console.log('📝 开始生成完整提示词...')
                  const promptResult = await window.ipcRenderer.invoke('generate-complete-prompt', {
                    folderPath: targetFolder,
                    nodeId: analysisResult.nodeId,
                    targetClassName: analysisResult.targetClassName,
                    figmaUrl: inputUrl
                  })

                  if (promptResult.success) {
                    console.log('✅ 完整提示词生成成功')

                    // 调用Claude API生成CSS代码
                    try {
                      console.log('🤖 开始调用Claude API生成CSS代码...')
                      const claudeResponse = await callClaude(promptResult.data.prompt, {
                        model: 'claude-3-5-sonnet-20241022',
                        maxTokens: 4000,
                        temperature: 0.3
                      })

                      console.log('✅ Claude API调用成功！')
                      console.log('🎨 Claude生成的CSS代码:')

                      console.log(claudeResponse)

                      // 获取当前的index.html的代码
                      try {
                        const indexHtmlPath = `${targetFolder}/index.html`
                        const fileResult = await window.ipcRenderer.invoke('read-file-content', indexHtmlPath)

                        if (fileResult.success) {
                          const originalCode = fileResult.data
                          console.log('📄 已读取index.html文件内容')

                          // 插入Claude生成的CSS样式到HTML文件
                          const updatedCode = insertCssIntoHtml(originalCode, claudeResponse)
                          console.log('📝 更新后的代码长度:', updatedCode.length)

                          // 写入更新后的代码到原文件中
                          if (updatedCode !== originalCode) {
                            try {
                              const writeResult = await window.ipcRenderer.invoke('write-file-content', {
                                filePath: indexHtmlPath,
                                content: updatedCode
                              })

                              if (writeResult.success) {
                                console.log('✅ 已成功写入更新后的代码到index.html')
                              } else {
                                console.error('❌ 写入文件失败:', writeResult.error)
                              }
                            } catch (writeError) {
                              console.error('❌ 写入文件时发生错误:', writeError)
                            }
                          } else {
                            console.error('❌ CSS样式插入失败，文件内容未发生变化')
                          }
                        } else {
                          console.error('❌ 读取index.html失败:', fileResult.error)
                        }
                      } catch (readError) {
                        console.error('❌ 读取文件时发生错误:', readError)
                      }
                      alert(`项目初始化完成！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\n🖼️ 已下载 ${summary.downloadedCount} 个图片到assets文件夹\n🔍 已分析HTML并获取Figma节点信息\n📝 已生成完整提示词\n🤖 已调用Claude API生成CSS代码\n� 已更新index.html文件\n�💡 请查看更新后的页面效果`)
                    } catch (claudeError) {
                      console.error('❌ Claude API调用失败:', claudeError)
                      alert(`项目初始化完成！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\n🖼️ 已下载 ${summary.downloadedCount} 个图片到assets文件夹\n🔍 已分析HTML并获取Figma节点信息\n📝 已生成完整提示词\n❌ Claude API调用失败: ${claudeError instanceof Error ? claudeError.message : '未知错误'}`)
                    }
                  } else {
                    console.warn('⚠️ 生成完整提示词失败:', promptResult.error)
                  }

                  console.log('✅ 完整提示词生成成功，项目初始化完成')
                  alert(`项目文件夹初始化成功！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\n🖼️ 已下载 ${summary.downloadedCount} 个图片到assets文件夹\n🔍 已分析HTML并获取Figma节点信息\n📝 已生成完整提示词`)
                } catch (promptError) {
                  console.warn('⚠️ 生成完整提示词失败:', promptError)
                  alert(`项目文件夹初始化成功！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\n🖼️ 已下载 ${summary.downloadedCount} 个图片到assets文件夹\n🔍 已分析HTML并获取Figma节点信息\n⚠️ 生成完整提示词失败: ${promptError instanceof Error ? promptError.message : '未知错误'}`)
                }
              } else {
                alert(`项目文件夹初始化成功！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\n🖼️ 已下载 ${summary.downloadedCount} 个图片到assets文件夹\n🔍 已分析HTML并获取Figma节点信息`)
              }
            } catch (recursiveError) {
              console.warn('⚠️ 递归处理失败:', recursiveError)
              alert(`项目初始化完成！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\n🖼️ 已下载 ${summary.downloadedCount} 个图片到assets文件夹\n⚠️ 递归处理失败: ${recursiveError instanceof Error ? recursiveError.message : '未知错误'}`)
            }
          } else {
            // 即使没有图片，也进行递归处理
            try {
              console.log('🔍 开始递归处理所有未定义样式的类名...')
              const emptySummary = { downloadedCount: 0, totalImages: 0 }
              await processUndefinedClassesRecursively(targetFolder, inputUrl, designWidth, emptySummary)

            } catch (recursiveError) {
              console.warn('⚠️ 递归处理失败:', recursiveError)
              alert(`项目初始化完成！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\nℹ️ 未发现可下载的图片\n⚠️ 递归处理失败: ${recursiveError instanceof Error ? recursiveError.message : '未知错误'}`)
            }
          }
        } catch (imageError) {
          console.warn('⚠️ 图片下载失败，但项目初始化成功:', imageError)

          // 即使图片下载失败，也尝试进行递归处理
          try {
            console.log('🔍 开始递归处理所有未定义样式的类名...')
            const failedSummary = { downloadedCount: 0, totalImages: 0, error: imageError }
            await processUndefinedClassesRecursively(targetFolder, inputUrl, designWidth, failedSummary)

          } catch (recursiveError) {
            console.warn('⚠️ 递归处理失败:', recursiveError)
            alert(`项目初始化完成！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\n⚠️ 图片下载失败: ${imageError instanceof Error ? imageError.message : '未知错误'}\n⚠️ 递归处理失败: ${recursiveError instanceof Error ? recursiveError.message : '未知错误'}`)
          }
        }
      } else {
        // 非Figma链接，也进行递归处理（如果有基础HTML结构）
        try {
          console.log('🔍 开始递归处理所有未定义样式的类名...')
          const nonFigmaSummary = { downloadedCount: 0, totalImages: 0, isNonFigma: true }
          await processUndefinedClassesRecursively(targetFolder, inputUrl, designWidth, nonFigmaSummary)

        } catch (recursiveError) {
          console.warn('⚠️ 递归处理失败:', recursiveError)
          alert(`项目初始化完成！\n✅ 已创建index.html和assets目录\n📐 设计稿宽度: ${designWidth}px\n⚠️ 递归处理失败: ${recursiveError instanceof Error ? recursiveError.message : '未知错误'}`)
        }
      }
    } else {
      console.log('📁 文件夹已存在:', targetFolder)
    }

    // 标记初始化成功
    initSuccess = true
  } catch (error) {
    console.error('❌ 文件夹验证或初始化失败:', error)
    alert(`文件夹验证或初始化失败: ${error instanceof Error ? error.message : '请检查路径是否正确'}`)
    return
  } finally {
    isValidating.value = false

    // 只有在初始化成功时才切换到对比界面
    if (initSuccess) {
      // 切换到对比界面
      showComparison.value = true
      showIframe.value = true

      // 设置iframe源为目标文件夹中的index.html
      iframeSrc.value = `file://${targetFolder}/index.html`

      // 等待DOM更新后设置iframe
      nextTick(() => {
        if (implementationIframe.value) {
          setupMobileIframe(implementationIframe.value)
        }
      })
    }
  }

  // 设置iframe源为目标文件夹中的index.html
  iframeSrc.value = `file://${targetFolder}/index.html`
  showComparison.value = true
  showIframe.value = true

  // 等待DOM更新后设置iframe
  nextTick(() => {
    if (implementationIframe.value) {
      setupMobileIframe(implementationIframe.value)
    }
  })
}

// 返回输入界面
const goBack = () => {
  showComparison.value = false
  showIframe.value = false
  designImageSrc.value = ''
}

// 选择文件夹
const selectFolder = async () => {
  try {
    const result = await window.ipcRenderer.invoke('show-folder-dialog')

    if (result.success && result.data) {
      selectedFolder.value = result.data.folderPath
    } else if (result.canceled) {
      // 用户取消选择，不做任何操作
      console.log('用户取消了文件夹选择')
    } else {
      console.error('选择文件夹失败:', result.error)
      alert('选择文件夹失败，请重试')
    }
  } catch (error) {
    console.error('调用文件夹选择对话框失败:', error)
    alert('选择文件夹失败，请重试')
  }
}

// 设置iframe为手机模式
const setupMobileIframe = (iframe: HTMLIFrameElement) => {
  iframe.onload = function() {
    try {
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (!iframeDoc) return

      // 检查是否已经有viewport meta标签
      let viewportMeta = iframeDoc.querySelector('meta[name="viewport"]') as HTMLMetaElement
      if (!viewportMeta) {
        viewportMeta = iframeDoc.createElement('meta')
        viewportMeta.name = 'viewport'
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
        iframeDoc.head.appendChild(viewportMeta)
      } else {
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      }

      // 添加移动端样式
      const mobileStyle = iframeDoc.createElement('style')
      mobileStyle.textContent = `
        body {
          -webkit-text-size-adjust: 100%;
          -ms-text-size-adjust: 100%;
          touch-action: manipulation;
        }
        * {
          -webkit-tap-highlight-color: transparent;
        }
      `
      iframeDoc.head.appendChild(mobileStyle)
    } catch (e) {
      // 跨域限制，无法修改iframe内容
      console.log('无法修改iframe内容，可能是跨域限制')
    }
  }
}

// 拖拽处理函数
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  const target = e.currentTarget as HTMLElement
  target.style.backgroundColor = '#f0f9ff'
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  const target = e.currentTarget as HTMLElement
  target.style.backgroundColor = '#fafafa'
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  const target = e.currentTarget as HTMLElement
  target.style.backgroundColor = '#fafafa'

  const files = e.dataTransfer?.files
  if (files && files.length > 0 && files[0].type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = function(e) {
      designImageSrc.value = e.target?.result as string
    }
    reader.readAsDataURL(files[0])
  }
}

// 页面加载完成后的初始化
onMounted(async () => {
  // let res = await getImageNodeIdsFromUrl("https://www.figma.com/design/mJ7a24qESpwJ5xUpZpuMpy/NCast---Podcast-App--Community-?node-id=46-6&t=5BxFmfFKJBcpjckl-4")
  // console.log(res)

  // let res2 = await getImageUrlsFromUrl("https://www.figma.com/design/mJ7a24qESpwJ5xUpZpuMpy/NCast---Podcast-App--Community-?node-id=46-6&t=5BxFmfFKJBcpjckl-4", res)
  // console.log(res2)

  // let res3 = await downloadImages(res2,"/Users/<USER>/Desktop/figmaimages")
  // console.log(res3)
})
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page {
  width: 100%;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: white;
}

/* 输入界面样式 */
.input-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #2d2d2d;
}

.input-section {
  text-align: center;
  padding: 40px;
  margin-top: -60px 
}

.title {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 16px;
  color: white;
}

.description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 28px;
  line-height: 1.5;
}

.input-group {
  display: flex;
  gap: 12px;
  width: 800px;
  margin-bottom: 32px;
  align-items: center;
}

.url-input {
  flex: 1;
  padding: 16px 20px;
  font-size: 16px;
  border: 2px solid #404040;
  border-radius: 8px;
  background: #1a1a1a;
  color: white;
  outline: none;
  transition: border-color 0.2s ease;
}

.url-input:focus {
  border-color: #007bff;
}

.url-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.start-btn {
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 500;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.start-btn:hover:not(:disabled) {
  background: #0056b3;
}

.start-btn:disabled {
  background: #404040;
  cursor: not-allowed;
}

.tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tips p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

/* 文件夹选择组件 */
.folder-selection {
  width: 800px;
  margin-bottom: 32px;
}

.folder-label {
  display: block;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-weight: 500;
}

.folder-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.folder-input {
  flex: 1;
  padding: 16px 20px;
  font-size: 16px;
  border: 2px solid #404040;
  border-radius: 8px;
  background: #1a1a1a;
  color: white;
  outline: none;
  transition: border-color 0.2s ease;
  cursor: pointer;
}

.folder-input:focus {
  border-color: #007bff;
}

.folder-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.folder-button {
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 500;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
  min-width: 120px;
}

.folder-button:hover {
  background: #218838;
}

.folder-button:active {
  background: #1e7e34;
}

.folder-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.folder-button:disabled:hover {
  background: #6c757d;
}

.folder-tips {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.folder-tips p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  line-height: 1.4;
}

.validating-tip {
  color: #007bff !important;
  font-weight: 500;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background: rgba(0, 0, 0, 0.9);
}

.container {
  width: 100%;
  height: 100vh;
  margin: 0;
  background: #fafafa;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
}

.comparison-container {
  display: flex;
  height: 100vh;
}

.left-section {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding-top: 55px;
  padding-right: 20px;
  background: #fafafa;
}

.right-section {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding-top: 55px;
  padding-left: 20px;
  background: #fafafa;
}

.phone-mockup {
  position: relative;
  width: 470px;
  height: 972px;
  background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
  border-radius: 50px;
  padding: 20px;
  box-shadow:
    0 0 0 2px #333,
    0 0 0 4px #1a1a1a,
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);
}

.phone-mockup::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 30px;
  background: #1a1a1a;
  border-radius: 15px;
  z-index: 2;
}

.phone-mockup::after {
  content: '';
  position: absolute;
  top: 22px;
  left: 50%;
  transform: translateX(-50%);
  width: 15px;
  height: 15px;
  background: #333;
  border-radius: 50%;
  z-index: 3;
}

.phone-screen {
  width: 430px;
  height: 932px;
  background: #000;
  border-radius: 35px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.design-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 0;
}

.phone-buttons {
  position: absolute;
  right: -3px;
  top: 120px;
}

.phone-button {
  width: 3px;
  background: #333;
  margin-bottom: 15px;
  border-radius: 2px;
}

.phone-button.volume-up {
  height: 30px;
}

.phone-button.volume-down {
  height: 30px;
}

.phone-button.power {
  position: absolute;
  right: 0;
  top: 80px;
  height: 50px;
}

.implementation-iframe {
  width: 430px;
  height: 932px;
  border: none;
  border-radius: 0;
  background: white;
  transform: scale(1);
  transform-origin: top left;
}

.phone-placeholder-iframe {
  color: #666;
  font-size: 14px;
  text-align: center;
}

.placeholder {
  text-align: center;
  color: #6b7280;
  font-size: 16px;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 10px;
  opacity: 0.5;
}

.phone-placeholder {
  color: #666;
  font-size: 14px;
  text-align: center;
}
</style>
